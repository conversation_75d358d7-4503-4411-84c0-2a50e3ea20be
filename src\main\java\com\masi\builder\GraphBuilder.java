package com.masi.builder;

import com.masi.graph.Graph;
import com.masi.graph.Node;
import com.masi.graph.Edge;

/**
 * Builder Pattern pour construire des graphes complexes
 */
public class GraphBuilder {
    private Graph graph;

    public GraphBuilder() {
        this.graph = new Graph();
    }

    public GraphBuilder addNode(String id, double x, double y) {
        Node node = new Node(id, x, y);
        graph.addNode(node);
        return this;
    }

    public GraphBuilder addEdge(String sourceId, String destId, double weight) {
        graph.addEdge(sourceId, destId, weight);
        return this;
    }

    public GraphBuilder addEdge(String sourceId, String destId) {
        graph.addEdge(sourceId, destId);
        return this;
    }

    public Graph build() {
        return graph;
    }

    // Méthodes pour créer des graphes prédéfinis
    public static GraphBuilder createGridGraph(int rows, int cols) {
        GraphBuilder builder = new GraphBuilder();
        
        // Créer les nœuds en grille
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                String nodeId = "N" + (i * cols + j + 1);
                builder.addNode(nodeId, j * 100 + 50, i * 100 + 50);
            }
        }
        
        // Créer les arêtes horizontales et verticales
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                String currentId = "N" + (i * cols + j + 1);
                
                // Arête horizontale (droite)
                if (j < cols - 1) {
                    String rightId = "N" + (i * cols + j + 2);
                    builder.addEdge(currentId, rightId);
                }
                
                // Arête verticale (bas)
                if (i < rows - 1) {
                    String bottomId = "N" + ((i + 1) * cols + j + 1);
                    builder.addEdge(currentId, bottomId);
                }
            }
        }
        
        return builder;
    }

    public static GraphBuilder createStarGraph(int numNodes) {
        GraphBuilder builder = new GraphBuilder();
        
        // Nœud central
        builder.addNode("N1", 300, 200);
        
        // Nœuds périphériques
        for (int i = 2; i <= numNodes; i++) {
            double angle = 2 * Math.PI * (i - 2) / (numNodes - 1);
            double x = 300 + 150 * Math.cos(angle);
            double y = 200 + 150 * Math.sin(angle);
            
            builder.addNode("N" + i, x, y);
            builder.addEdge("N1", "N" + i);
        }
        
        return builder;
    }

    public static GraphBuilder createCompleteGraph(int numNodes) {
        GraphBuilder builder = new GraphBuilder();
        
        // Créer les nœuds en cercle
        for (int i = 1; i <= numNodes; i++) {
            double angle = 2 * Math.PI * (i - 1) / numNodes;
            double x = 300 + 150 * Math.cos(angle);
            double y = 200 + 150 * Math.sin(angle);
            
            builder.addNode("N" + i, x, y);
        }
        
        // Créer toutes les arêtes possibles
        for (int i = 1; i <= numNodes; i++) {
            for (int j = i + 1; j <= numNodes; j++) {
                builder.addEdge("N" + i, "N" + j);
            }
        }
        
        return builder;
    }
}
