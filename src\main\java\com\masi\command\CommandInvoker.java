package com.masi.command;

import java.util.Stack;

/**
 * Invoker pour le pattern Command - gère l'historique des commandes
 */
public class CommandInvoker {
    private Stack<Command> commandHistory;
    private Stack<Command> undoHistory;

    public CommandInvoker() {
        this.commandHistory = new Stack<>();
        this.undoHistory = new Stack<>();
    }

    /**
     * Exécute une commande et l'ajoute à l'historique
     */
    public void executeCommand(Command command) {
        command.execute();
        commandHistory.push(command);
        // Vider l'historique undo car on a une nouvelle commande
        undoHistory.clear();
    }

    /**
     * Annule la dernière commande
     */
    public boolean undo() {
        if (!commandHistory.isEmpty()) {
            Command command = commandHistory.pop();
            command.undo();
            undoHistory.push(command);
            return true;
        }
        return false;
    }

    /**
     * Refait la dernière commande annulée
     */
    public boolean redo() {
        if (!undoHistory.isEmpty()) {
            Command command = undoHistory.pop();
            command.execute();
            commandHistory.push(command);
            return true;
        }
        return false;
    }

    /**
     * Vérifie s'il y a des commandes à annuler
     */
    public boolean canUndo() {
        return !commandHistory.isEmpty();
    }

    /**
     * Vérifie s'il y a des commandes à refaire
     */
    public boolean canRedo() {
        return !undoHistory.isEmpty();
    }

    /**
     * Retourne la description de la dernière commande
     */
    public String getLastCommandDescription() {
        if (!commandHistory.isEmpty()) {
            return commandHistory.peek().getDescription();
        }
        return "No commands";
    }

    /**
     * Vide l'historique des commandes
     */
    public void clearHistory() {
        commandHistory.clear();
        undoHistory.clear();
    }

    /**
     * Retourne le nombre de commandes dans l'historique
     */
    public int getHistorySize() {
        return commandHistory.size();
    }
}
