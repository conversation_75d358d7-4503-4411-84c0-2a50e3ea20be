package com.masi.Controller;

import com.masi.decorator.BorderDecorator;
import com.masi.decorator.ShadowDecorator;
import com.masi.decorator.Shape;
import com.masi.decorator.Rectangle;
import com.masi.logger.ConsoleLogger;
import com.masi.logger.DatabaseLogger;
import com.masi.logger.FileLogger;
import com.masi.logger.Logger;
import com.masi.observer.DrawingModel;
import com.masi.observer.Observer;
import javafx.fxml.FXML;
import javafx.scene.control.ChoiceBox;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import javafx.scene.shape.Line;
import javafx.animation.FadeTransition;
import javafx.util.Duration;

public class DrawingController implements Observer {
    @FXML
    private Pane drawingPane;
    @FXML
    private ChoiceBox<String> shapeChoice;
    @FXML
    private ChoiceBox<String> logChoice;
    @FXML
    private ChoiceBox<String> decoratorChoice;

    private DrawingModel model = new DrawingModel();
    private Logger logger;

    public void initialize() {
        // Initialize ChoiceBoxes
        shapeChoice.getItems().addAll("Rectangle", "Circle", "Line");
        logChoice.getItems().addAll("Console", "File", "Database");
        decoratorChoice.getItems().addAll("None", "Border", "Shadow", "Both");

        // Set default values
        shapeChoice.setValue("Rectangle");
        logChoice.setValue("Console");
        decoratorChoice.setValue("None");

        // Set logger based on selection
        logChoice.setOnAction(e -> setLogger(logChoice.getValue()));

        // Set default logger
        setLogger("Console");
        model.addObserver(this);

        // Add some styling to the pane
        drawingPane.setStyle("-fx-background-color: white; -fx-border-color: #dddddd; -fx-border-width: 1px;");
    }

    @FXML
    private void handleDraw() {
        // Clear previous drawings
        drawingPane.getChildren().clear();

        String shapeType = shapeChoice.getValue();
        String decorator = decoratorChoice.getValue();

        // Create base shape based on selection
        Shape baseShape = new Rectangle();

        // Apply decorator based on selection
        if ("Border".equals(decorator)) {
            baseShape = new BorderDecorator(baseShape);
        } else if ("Shadow".equals(decorator)) {
            baseShape = new ShadowDecorator(baseShape);
        } else if ("Both".equals(decorator)) {
            baseShape = new BorderDecorator(new ShadowDecorator(baseShape));
        }

        // Log the action
        baseShape.draw();
        logger.log("Drew " + shapeType + " with " + decorator);
        model.setSelectedShape(shapeType);

        // Draw the actual visual shape on the pane
        drawVisualShape(shapeType, decorator);
    }

    private void drawVisualShape(String shapeType, String decorator) {
        javafx.scene.Node shape = null;

        // Center coordinates
        double centerX = drawingPane.getWidth() / 2;
        double centerY = drawingPane.getHeight() / 2;

        // Create the visual shape
        switch (shapeType) {
            case "Rectangle":
                javafx.scene.shape.Rectangle rect = new javafx.scene.shape.Rectangle(centerX - 75, centerY - 50, 150, 100);
                rect.setFill(Color.LIGHTBLUE);
                rect.setStroke(Color.BLUE);
                shape = rect;
                break;
            case "Circle":
                Circle circle = new Circle(centerX, centerY, 60);
                circle.setFill(Color.LIGHTGREEN);
                circle.setStroke(Color.GREEN);
                shape = circle;
                break;
            case "Line":
                Line line = new Line(centerX - 75, centerY, centerX + 75, centerY);
                line.setStrokeWidth(3);
                line.setStroke(Color.RED);
                shape = line;
                break;
        }

        // Apply decorators visually
        if (shape != null) {
            if (decorator.contains("Border")) {
                shape.setStyle("-fx-border-color: black; -fx-border-width: 3px;");
            }
            if (decorator.contains("Shadow")) {
                shape.setStyle(shape.getStyle() + "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.8), 10, 0, 0, 0);");
            }

            // Add the shape to the pane with animation
            drawingPane.getChildren().add(shape);

            // Add fade-in animation
            FadeTransition fadeIn = new FadeTransition(Duration.millis(1000), shape);
            fadeIn.setFromValue(0.0);
            fadeIn.setToValue(1.0);
            fadeIn.play();
        }
    }

    private void setLogger(String type) {
        switch (type) {
            case "Console":
                logger = new ConsoleLogger();
                break;
            case "File":
                logger = new FileLogger();
                break;
            case "Database":
                logger = new DatabaseLogger();
                break;
            default:
                logger = new ConsoleLogger();
        }
        logger.log("Logger changé vers: " + type);
    }

    @Override
    public void update() {
        // Update UI when model changes
        System.out.println("Model updated: " + model.getSelectedShape());
    }
}