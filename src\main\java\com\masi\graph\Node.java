package com.masi.graph;

import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import javafx.scene.text.Text;
import java.util.Objects;

/**
 * Représente un nœud dans le graphe
 */
public class Node {
    private String id;
    private double x;
    private double y;
    private Circle visualNode;
    private Text label;
    private boolean isSelected;
    private boolean isInPath;

    public Node(String id, double x, double y) {
        this.id = id;
        this.x = x;
        this.y = y;
        this.isSelected = false;
        this.isInPath = false;
        createVisualElements();
    }

    private void createVisualElements() {
        // Créer le cercle visuel
        visualNode = new Circle(x, y, 20);
        visualNode.setFill(Color.LIGHTBLUE);
        visualNode.setStroke(Color.DARKBLUE);
        visualNode.setStrokeWidth(2);

        // Créer le label
        label = new Text(x - 5, y + 5, id);
        label.setFill(Color.BLACK);
    }

    public void setSelected(boolean selected) {
        this.isSelected = selected;
        updateVisualState();
    }

    public void setInPath(boolean inPath) {
        this.isInPath = inPath;
        updateVisualState();
    }

    private void updateVisualState() {
        if (isInPath) {
            visualNode.setFill(Color.GOLD);
            visualNode.setStroke(Color.ORANGE);
        } else if (isSelected) {
            visualNode.setFill(Color.LIGHTGREEN);
            visualNode.setStroke(Color.GREEN);
        } else {
            visualNode.setFill(Color.LIGHTBLUE);
            visualNode.setStroke(Color.DARKBLUE);
        }
    }

    public double distanceTo(Node other) {
        double dx = this.x - other.x;
        double dy = this.y - other.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    // Getters et setters
    public String getId() { return id; }
    public double getX() { return x; }
    public double getY() { return y; }
    public Circle getVisualNode() { return visualNode; }
    public Text getLabel() { return label; }
    public boolean isSelected() { return isSelected; }
    public boolean isInPath() { return isInPath; }

    public void setPosition(double x, double y) {
        this.x = x;
        this.y = y;
        visualNode.setCenterX(x);
        visualNode.setCenterY(y);
        label.setX(x - 5);
        label.setY(y + 5);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Node node = (Node) obj;
        return Objects.equals(id, node.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Node{" + "id='" + id + '\'' + ", x=" + x + ", y=" + y + '}';
    }
}
