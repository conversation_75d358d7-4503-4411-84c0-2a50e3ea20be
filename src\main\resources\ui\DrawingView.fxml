<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.geometry.Insets?>

<AnchorPane xmlns="http://javafx.com/javafx"
            xmlns:fx="http://javafx.com/fxml"
            fx:controller="com.masi.Controller.DrawingController"
            prefHeight="400.0" prefWidth="600.0">

    <children>
        <VBox alignment="CENTER" spacing="10">
            <padding>
                <Insets top="10" right="10" bottom="10" left="10"/>
            </padding>
            <ChoiceBox fx:id="shapeChoice" />
            <ChoiceBox fx:id="logChoice" />
            <ChoiceBox fx:id="decoratorChoice" />
            <Button text="Draw" onAction="#handleDraw"/>

            <Pane fx:id="drawingPane" prefHeight="400" prefWidth="600" style="-fx-background-color: white;" />
        </VBox>
    </children>
</AnchorPane>