package com.masi.algorithm;

import com.masi.graph.Graph;
import com.masi.graph.Node;
import com.masi.graph.Edge;
import java.util.*;

/**
 * Implémentation de l'algorithme de Dijkstra pour trouver le plus court chemin
 */
public class DijkstraAlgorithm implements PathFindingStrategy {
    private double totalDistance;

    @Override
    public List<Node> findShortestPath(Graph graph, Node start, Node end) {
        if (start == null || end == null) {
            return null;
        }

        // Initialisation
        Map<Node, Double> distances = new HashMap<>();
        Map<Node, Node> previous = new HashMap<>();
        PriorityQueue<NodeDistance> queue = new PriorityQueue<>(Comparator.comparing(nd -> nd.distance));
        Set<Node> visited = new HashSet<>();

        // Initialiser toutes les distances à l'infini
        for (Node node : graph.getNodes()) {
            distances.put(node, Double.POSITIVE_INFINITY);
        }
        distances.put(start, 0.0);
        queue.offer(new NodeDistance(start, 0.0));

        while (!queue.isEmpty()) {
            NodeDistance current = queue.poll();
            Node currentNode = current.node;

            if (visited.contains(currentNode)) {
                continue;
            }
            visited.add(currentNode);

            // Si on a atteint la destination
            if (currentNode.equals(end)) {
                break;
            }

            // Examiner tous les voisins
            for (Edge edge : graph.getEdgesFromNode(currentNode)) {
                Node neighbor = edge.getDestination();
                if (visited.contains(neighbor)) {
                    continue;
                }

                double newDistance = distances.get(currentNode) + edge.getWeight();
                if (newDistance < distances.get(neighbor)) {
                    distances.put(neighbor, newDistance);
                    previous.put(neighbor, currentNode);
                    queue.offer(new NodeDistance(neighbor, newDistance));
                }
            }
        }

        // Reconstruire le chemin
        List<Node> path = reconstructPath(previous, start, end);
        if (path != null) {
            totalDistance = distances.get(end);
        }
        return path;
    }

    private List<Node> reconstructPath(Map<Node, Node> previous, Node start, Node end) {
        List<Node> path = new ArrayList<>();
        Node current = end;

        while (current != null) {
            path.add(0, current);
            current = previous.get(current);
        }

        // Vérifier si un chemin existe
        if (path.isEmpty() || !path.get(0).equals(start)) {
            return null;
        }

        return path;
    }

    @Override
    public String getAlgorithmName() {
        return "Dijkstra";
    }

    @Override
    public double getTotalDistance() {
        return totalDistance;
    }

    /**
     * Classe interne pour représenter un nœud avec sa distance
     */
    private static class NodeDistance {
        Node node;
        double distance;

        NodeDistance(Node node, double distance) {
            this.node = node;
            this.distance = distance;
        }
    }
}
