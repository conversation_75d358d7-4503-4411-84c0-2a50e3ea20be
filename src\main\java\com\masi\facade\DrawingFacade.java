package com.masi.facade;

import com.masi.graph.Graph;
import com.masi.graph.Node;
import com.masi.algorithm.*;
import com.masi.builder.GraphBuilder;
import com.masi.logger.Logger;
import com.masi.logger.ConsoleLogger;
import java.util.List;

/**
 * Facade Pattern pour simplifier l'utilisation du système de graphe
 */
public class DrawingFacade {
    private Graph graph;
    private PathFindingStrategy algorithm;
    private Logger logger;

    public DrawingFacade() {
        this.graph = new Graph();
        this.algorithm = new DijkstraAlgorithm();
        this.logger = new ConsoleLogger();
    }

    /**
     * Crée un graphe simple avec quelques nœuds connectés
     */
    public void createSimpleGraph() {
        GraphBuilder builder = new GraphBuilder();
        Graph newGraph = builder
            .addNode("A", 100, 100)
            .addNode("B", 200, 100)
            .addNode("C", 300, 100)
            .addNode("D", 200, 200)
            .addEdge("A", "B")
            .addEdge("B", "C")
            .addEdge("B", "D")
            .addEdge("A", "D")
            .build();
        
        this.graph = newGraph;
        logger.log("Graphe simple créé avec 4 nœuds");
    }

    /**
     * Crée un graphe en grille
     */
    public void createGridGraph(int rows, int cols) {
        this.graph = GraphBuilder.createGridGraph(rows, cols).build();
        logger.log("Graphe en grille " + rows + "x" + cols + " créé");
    }

    /**
     * Crée un graphe en étoile
     */
    public void createStarGraph(int numNodes) {
        this.graph = GraphBuilder.createStarGraph(numNodes).build();
        logger.log("Graphe en étoile avec " + numNodes + " nœuds créé");
    }

    /**
     * Crée un graphe complet
     */
    public void createCompleteGraph(int numNodes) {
        this.graph = GraphBuilder.createCompleteGraph(numNodes).build();
        logger.log("Graphe complet avec " + numNodes + " nœuds créé");
    }

    /**
     * Change l'algorithme de recherche de chemin
     */
    public void setAlgorithm(String algorithmName) {
        switch (algorithmName.toLowerCase()) {
            case "dijkstra":
                this.algorithm = new DijkstraAlgorithm();
                break;
            case "astar":
            case "a*":
                this.algorithm = new AStarAlgorithm();
                break;
            case "bfs":
                this.algorithm = new BFSAlgorithm();
                break;
            default:
                logger.log("Algorithme inconnu, utilisation de Dijkstra par défaut");
                this.algorithm = new DijkstraAlgorithm();
        }
        logger.log("Algorithme changé vers: " + this.algorithm.getAlgorithmName());
    }

    /**
     * Trouve et affiche le plus court chemin
     */
    public PathResult findShortestPath(String startId, String endId) {
        Node startNode = graph.getNode(startId);
        Node endNode = graph.getNode(endId);

        if (startNode == null || endNode == null) {
            logger.log("Erreur: Nœud(s) introuvable(s)");
            return null;
        }

        List<Node> path = algorithm.findShortestPath(graph, startNode, endNode);
        double distance = algorithm.getTotalDistance();

        if (path != null) {
            graph.highlightPath(path);
            String pathString = pathToString(path);
            logger.log("Chemin trouvé: " + pathString + " (Distance: " + String.format("%.2f", distance) + ")");
            
            return new PathResult(path, distance, algorithm.getAlgorithmName());
        } else {
            logger.log("Aucun chemin trouvé entre " + startId + " et " + endId);
            return null;
        }
    }

    /**
     * Compare tous les algorithmes sur le même chemin
     */
    public void compareAlgorithms(String startId, String endId) {
        logger.log("=== Comparaison des algorithmes ===");
        
        String[] algorithms = {"Dijkstra", "A*", "BFS"};
        
        for (String algo : algorithms) {
            setAlgorithm(algo);
            PathResult result = findShortestPath(startId, endId);
            
            if (result != null) {
                logger.log(algo + ": " + result.getPathString() + 
                          " (Distance: " + String.format("%.2f", result.getDistance()) + ")");
            } else {
                logger.log(algo + ": Aucun chemin trouvé");
            }
        }
        
        logger.log("=== Fin de la comparaison ===");
    }

    /**
     * Efface le chemin surligné
     */
    public void clearPath() {
        graph.clearPath();
        logger.log("Chemin effacé");
    }

    /**
     * Obtient des statistiques sur le graphe
     */
    public void showGraphStats() {
        int nodeCount = graph.getNodeCount();
        int edgeCount = graph.getEdgeCount();
        
        logger.log("=== Statistiques du graphe ===");
        logger.log("Nombre de nœuds: " + nodeCount);
        logger.log("Nombre d'arêtes: " + edgeCount);
        logger.log("Densité: " + String.format("%.2f", (double) edgeCount / (nodeCount * (nodeCount - 1) / 2)));
        logger.log("==============================");
    }

    private String pathToString(List<Node> path) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < path.size(); i++) {
            sb.append(path.get(i).getId());
            if (i < path.size() - 1) {
                sb.append(" → ");
            }
        }
        return sb.toString();
    }

    // Getters
    public Graph getGraph() { return graph; }
    public PathFindingStrategy getAlgorithm() { return algorithm; }

    /**
     * Classe interne pour encapsuler les résultats de recherche de chemin
     */
    public static class PathResult {
        private List<Node> path;
        private double distance;
        private String algorithmName;

        public PathResult(List<Node> path, double distance, String algorithmName) {
            this.path = path;
            this.distance = distance;
            this.algorithmName = algorithmName;
        }

        public List<Node> getPath() { return path; }
        public double getDistance() { return distance; }
        public String getAlgorithmName() { return algorithmName; }
        
        public String getPathString() {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < path.size(); i++) {
                sb.append(path.get(i).getId());
                if (i < path.size() - 1) {
                    sb.append(" → ");
                }
            }
            return sb.toString();
        }
    }
}
