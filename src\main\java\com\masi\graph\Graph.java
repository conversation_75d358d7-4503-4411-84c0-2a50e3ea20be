package com.masi.graph;

import java.util.*;

/**
 * Représente un graphe avec des nœuds et des arêtes
 */
public class Graph {
    private Map<String, Node> nodes;
    private List<Edge> edges;
    private Map<Node, List<Edge>> adjacencyList;

    public Graph() {
        this.nodes = new HashMap<>();
        this.edges = new ArrayList<>();
        this.adjacencyList = new HashMap<>();
    }

    public void addNode(Node node) {
        nodes.put(node.getId(), node);
        adjacencyList.put(node, new ArrayList<>());
    }

    public void addEdge(Edge edge) {
        edges.add(edge);
        
        // Ajouter à la liste d'adjacence
        adjacencyList.get(edge.getSource()).add(edge);
        
        // Pour un graphe non dirigé, ajouter aussi l'arête inverse
        Edge reverseEdge = new Edge(edge.getDestination(), edge.getSource(), edge.getWeight());
        adjacencyList.get(edge.getDestination()).add(reverseEdge);
    }

    public void addEdge(String sourceId, String destId, double weight) {
        Node source = nodes.get(sourceId);
        Node dest = nodes.get(destId);
        if (source != null && dest != null) {
            Edge edge = new Edge(source, dest, weight);
            addEdge(edge);
        }
    }

    public void addEdge(String sourceId, String destId) {
        Node source = nodes.get(sourceId);
        Node dest = nodes.get(destId);
        if (source != null && dest != null) {
            Edge edge = new Edge(source, dest);
            addEdge(edge);
        }
    }

    public Node getNode(String id) {
        return nodes.get(id);
    }

    public List<Edge> getEdgesFromNode(Node node) {
        return adjacencyList.getOrDefault(node, new ArrayList<>());
    }

    public List<Node> getNeighbors(Node node) {
        List<Node> neighbors = new ArrayList<>();
        List<Edge> nodeEdges = getEdgesFromNode(node);
        for (Edge edge : nodeEdges) {
            neighbors.add(edge.getDestination());
        }
        return neighbors;
    }

    public void clearPath() {
        for (Node node : nodes.values()) {
            node.setInPath(false);
        }
        for (Edge edge : edges) {
            edge.setInPath(false);
        }
    }

    public void highlightPath(List<Node> path) {
        clearPath();
        
        // Marquer les nœuds du chemin
        for (Node node : path) {
            node.setInPath(true);
        }
        
        // Marquer les arêtes du chemin
        for (int i = 0; i < path.size() - 1; i++) {
            Node current = path.get(i);
            Node next = path.get(i + 1);
            
            for (Edge edge : edges) {
                if ((edge.getSource().equals(current) && edge.getDestination().equals(next)) ||
                    (edge.getSource().equals(next) && edge.getDestination().equals(current))) {
                    edge.setInPath(true);
                    break;
                }
            }
        }
    }

    public void clear() {
        nodes.clear();
        edges.clear();
        adjacencyList.clear();
    }

    // Getters
    public Collection<Node> getNodes() {
        return nodes.values();
    }

    public List<Edge> getEdges() {
        return edges;
    }

    public int getNodeCount() {
        return nodes.size();
    }

    public int getEdgeCount() {
        return edges.size();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Graph with ").append(nodes.size()).append(" nodes and ").append(edges.size()).append(" edges:\n");
        for (Node node : nodes.values()) {
            sb.append("Node ").append(node.getId()).append(" -> ");
            List<Edge> nodeEdges = getEdgesFromNode(node);
            for (Edge edge : nodeEdges) {
                sb.append(edge.getDestination().getId()).append("(").append(edge.getWeight()).append(") ");
            }
            sb.append("\n");
        }
        return sb.toString();
    }
}
