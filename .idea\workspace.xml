<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c6861ad2-44f8-437b-82e1-e7c8248bdc2c" name="Changes" comment="Merge remote-tracking branch 'origin/Partb' into Partb&#10;&#10;# Conflicts:&#10;#&#9;src/main/java/com/masi/Main.java" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="FxmlFile" />
        <option value="Interface" />
        <option value="Class" />
        <option value="AnnotationType" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="Rahma Bradai &lt;<EMAIL>&gt;" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="Partie-A" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;sirinedaik55&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/RahmaBradai724/masi-drawing-app.git&quot;,
    &quot;accountId&quot;: &quot;e3fe416d-fe80-417e-9745-73b9abe92b2d&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2yHBBrU9v9kvmIfs0kgz89sDVvC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.Main.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "Merge-part-1",
    "last_opened_file_path": "C:/Users/<USER>/Documents/DesignPattern/masi-drawing-app",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Libraries",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="com.masi" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="Main" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="17" />
      <option name="MAIN_CLASS_NAME" value="com.masi.Main" />
      <module name="drawingapp" />
      <option name="PROGRAM_PARAMETERS" value="--module-path &quot;C:\Program Files\javafx-sdk-21.0.7\lib&quot; --add-modules=javafx.controls,javafx.fxml " />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.masi.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.Main" />
        <item itemvalue="Application.Main" />
        <item itemvalue="Application.Main" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c6861ad2-44f8-437b-82e1-e7c8248bdc2c" name="Changes" comment="" />
      <created>1749481487547</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749481487547</updated>
      <workItem from="1749481488178" duration="11470000" />
      <workItem from="1749562995729" duration="721000" />
      <workItem from="1749565210698" duration="1239000" />
    </task>
    <task id="LOCAL-00001" summary="implémentation des patterns Strategy, Observer, Decorator et interface JavaFX">
      <option name="closed" value="true" />
      <created>1749513668601</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749513668601</updated>
    </task>
    <task id="LOCAL-00002" summary="implémentation des patterns Strategy, Observer, Decorator et interface JavaFX">
      <option name="closed" value="true" />
      <created>1749513701061</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749513701061</updated>
    </task>
    <task id="LOCAL-00003" summary="implémentation des patterns Strategy, Observer, Decorator et interface JavaFX">
      <option name="closed" value="true" />
      <created>1749513723006</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1749513723006</updated>
    </task>
    <task id="LOCAL-00004" summary="Merge remote-tracking branch 'origin/Partb' into Partb&#10;&#10;# Conflicts:&#10;#&#9;src/main/java/com/masi/Main.java">
      <option name="closed" value="true" />
      <created>1749513856427</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1749513856427</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/Partb" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Rename package to com.masi and add VCS configuration" />
    <MESSAGE value="implémentation des patterns Strategy, Observer, Decorator et interface JavaFX" />
    <MESSAGE value="Merge remote-tracking branch 'origin/Partb' into Partb&#10;&#10;# Conflicts:&#10;#&#9;src/main/java/com/masi/Main.java" />
    <option name="LAST_COMMIT_MESSAGE" value="Merge remote-tracking branch 'origin/Partb' into Partb&#10;&#10;# Conflicts:&#10;#&#9;src/main/java/com/masi/Main.java" />
  </component>
</project>