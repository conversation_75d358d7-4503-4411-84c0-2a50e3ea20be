package com.masi.algorithm;

import com.masi.graph.Graph;
import com.masi.graph.Node;
import com.masi.graph.Edge;
import java.util.*;

/**
 * Implémentation de l'algorithme A* pour trouver le plus court chemin
 */
public class AStarAlgorithm implements PathFindingStrategy {
    private double totalDistance;

    @Override
    public List<Node> findShortestPath(Graph graph, Node start, Node end) {
        if (start == null || end == null) {
            return null;
        }

        // Initialisation
        Map<Node, Double> gScore = new HashMap<>(); // Distance réelle depuis le début
        Map<Node, Double> fScore = new HashMap<>(); // gScore + heuristique
        Map<Node, Node> previous = new HashMap<>();
        PriorityQueue<NodeScore> openSet = new PriorityQueue<>(Comparator.comparing(ns -> ns.fScore));
        Set<Node> closedSet = new HashSet<>();

        // Initialiser toutes les distances
        for (Node node : graph.getNodes()) {
            gScore.put(node, Double.POSITIVE_INFINITY);
            fScore.put(node, Double.POSITIVE_INFINITY);
        }

        gScore.put(start, 0.0);
        fScore.put(start, heuristic(start, end));
        openSet.offer(new NodeScore(start, fScore.get(start)));

        while (!openSet.isEmpty()) {
            NodeScore current = openSet.poll();
            Node currentNode = current.node;

            if (closedSet.contains(currentNode)) {
                continue;
            }

            closedSet.add(currentNode);

            // Si on a atteint la destination
            if (currentNode.equals(end)) {
                List<Node> path = reconstructPath(previous, start, end);
                totalDistance = gScore.get(end);
                return path;
            }

            // Examiner tous les voisins
            for (Edge edge : graph.getEdgesFromNode(currentNode)) {
                Node neighbor = edge.getDestination();
                
                if (closedSet.contains(neighbor)) {
                    continue;
                }

                double tentativeGScore = gScore.get(currentNode) + edge.getWeight();

                if (tentativeGScore < gScore.get(neighbor)) {
                    previous.put(neighbor, currentNode);
                    gScore.put(neighbor, tentativeGScore);
                    fScore.put(neighbor, tentativeGScore + heuristic(neighbor, end));
                    
                    openSet.offer(new NodeScore(neighbor, fScore.get(neighbor)));
                }
            }
        }

        // Aucun chemin trouvé
        return null;
    }

    /**
     * Fonction heuristique : distance euclidienne
     */
    private double heuristic(Node a, Node b) {
        return a.distanceTo(b);
    }

    private List<Node> reconstructPath(Map<Node, Node> previous, Node start, Node end) {
        List<Node> path = new ArrayList<>();
        Node current = end;

        while (current != null) {
            path.add(0, current);
            current = previous.get(current);
        }

        // Vérifier si un chemin existe
        if (path.isEmpty() || !path.get(0).equals(start)) {
            return null;
        }

        return path;
    }

    @Override
    public String getAlgorithmName() {
        return "A* (A-Star)";
    }

    @Override
    public double getTotalDistance() {
        return totalDistance;
    }

    /**
     * Classe interne pour représenter un nœud avec son score F
     */
    private static class NodeScore {
        Node node;
        double fScore;

        NodeScore(Node node, double fScore) {
            this.node = node;
            this.fScore = fScore;
        }
    }
}
