package com.masi.graph;

import javafx.scene.paint.Color;
import javafx.scene.shape.Line;
import javafx.scene.text.Text;

/**
 * Représente une arête dans le graphe
 */
public class Edge {
    private Node source;
    private Node destination;
    private double weight;
    private Line visualEdge;
    private Text weightLabel;
    private boolean isInPath;

    public Edge(Node source, Node destination, double weight) {
        this.source = source;
        this.destination = destination;
        this.weight = weight;
        this.isInPath = false;
        createVisualElements();
    }

    public Edge(Node source, Node destination) {
        this(source, destination, source.distanceTo(destination));
    }

    private void createVisualElements() {
        // Créer la ligne visuelle
        visualEdge = new Line(
            source.getX(), source.getY(),
            destination.getX(), destination.getY()
        );
        visualEdge.setStroke(Color.GRAY);
        visualEdge.setStrokeWidth(2);

        // Créer le label du poids
        double midX = (source.getX() + destination.getX()) / 2;
        double midY = (source.getY() + destination.getY()) / 2;
        weightLabel = new Text(midX, midY, String.format("%.1f", weight));
        weightLabel.setFill(Color.BLACK);
    }

    public void setInPath(boolean inPath) {
        this.isInPath = inPath;
        updateVisualState();
    }

    private void updateVisualState() {
        if (isInPath) {
            visualEdge.setStroke(Color.RED);
            visualEdge.setStrokeWidth(4);
            weightLabel.setFill(Color.RED);
        } else {
            visualEdge.setStroke(Color.GRAY);
            visualEdge.setStrokeWidth(2);
            weightLabel.setFill(Color.BLACK);
        }
    }

    public void updateVisualPosition() {
        visualEdge.setStartX(source.getX());
        visualEdge.setStartY(source.getY());
        visualEdge.setEndX(destination.getX());
        visualEdge.setEndY(destination.getY());

        double midX = (source.getX() + destination.getX()) / 2;
        double midY = (source.getY() + destination.getY()) / 2;
        weightLabel.setX(midX);
        weightLabel.setY(midY);
    }

    // Getters
    public Node getSource() { return source; }
    public Node getDestination() { return destination; }
    public double getWeight() { return weight; }
    public Line getVisualEdge() { return visualEdge; }
    public Text getWeightLabel() { return weightLabel; }
    public boolean isInPath() { return isInPath; }

    public void setWeight(double weight) {
        this.weight = weight;
        weightLabel.setText(String.format("%.1f", weight));
    }

    @Override
    public String toString() {
        return "Edge{" +
                "source=" + source.getId() +
                ", destination=" + destination.getId() +
                ", weight=" + weight +
                '}';
    }
}
