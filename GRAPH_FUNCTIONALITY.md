# 📊 Fonctionnalité de Graphe et Plus Court Chemin

## 🎯 Vue d'ensemble

Cette application de dessin a été étendue avec une fonctionnalité complète de création et d'analyse de graphes, démontrant l'utilisation de plusieurs design patterns avancés.

## 🏗️ Design Patterns Implémentés

### 1. **Strategy Pattern** - Algorithmes de Plus Court Chemin
- **Interface** : `PathFindingStrategy`
- **Implémentations** :
  - `DijkstraAlgorithm` : Algorithme de Dijkstra (optimal pour graphes pondérés)
  - `AStarAlgorithm` : Algorithme A* avec heuristique euclidienne
  - `BFSAlgorithm` : Parcours en largeur (optimal pour graphes non pondérés)

### 2. **Command Pattern** - Opérations sur le Graphe
- **Interface** : `Command`
- **Implémentations** :
  - `AddNodeCommand` : Ajouter un nœud
  - `AddEdgeCommand` : Ajouter une arête
- **Invoker** : `CommandInvoker` (support undo/redo)

### 3. **Observer Pattern** - Notifications d'état
- Mise à jour automatique de l'interface lors des changements

### 4. **Decorator Pattern** - Effets visuels
- Application d'effets sur les formes dessinées

### 5. **MVC Pattern** - Architecture générale
- Séparation claire des responsabilités

## 🚀 Comment Utiliser

### Mode Graphe

1. **Sélectionner le mode** : Choisir "Graph" dans la liste déroulante "Shape"

2. **Créer des nœuds** :
   - Cliquer sur la zone de dessin pour créer un nouveau nœud
   - Les nœuds sont automatiquement nommés N1, N2, N3, etc.

3. **Créer des arêtes** :
   - Cliquer sur un premier nœud (il devient vert)
   - Cliquer sur un second nœud pour créer une arête
   - Le poids de l'arête est calculé automatiquement (distance euclidienne)

### Recherche de Plus Court Chemin

1. **Choisir l'algorithme** : Sélectionner Dijkstra, A*, ou BFS

2. **Spécifier les nœuds** :
   - Entrer l'ID du nœud de départ (ex: N1)
   - Entrer l'ID du nœud d'arrivée (ex: N5)

3. **Lancer la recherche** : Cliquer sur "Find Path"

4. **Visualiser le résultat** :
   - Le chemin optimal est surligné en rouge/or
   - La distance totale est affichée
   - Les détails sont loggés selon la stratégie choisie

5. **Effacer le chemin** : Cliquer sur "Clear Path"

## 📈 Exemple d'Utilisation

```
1. Créer les nœuds : N1, N2, N3, N4, N5
2. Créer les arêtes : N1-N2, N2-N3, N1-N4, N4-N5, N3-N5
3. Chercher le chemin de N1 à N5
4. Comparer les résultats avec différents algorithmes
```

## 🔍 Comparaison des Algorithmes

| Algorithme | Complexité | Optimal | Heuristique | Cas d'usage |
|------------|------------|---------|-------------|-------------|
| **Dijkstra** | O((V+E)log V) | ✅ | ❌ | Graphes pondérés généraux |
| **A*** | O(b^d) | ✅* | ✅ | Graphes avec coordonnées |
| **BFS** | O(V+E) | ✅** | ❌ | Graphes non pondérés |

*Optimal si l'heuristique est admissible
**Optimal pour le nombre d'arêtes, pas la distance

## 🎨 Fonctionnalités Visuelles

- **Nœuds** : Cercles bleus avec labels
- **Arêtes** : Lignes grises avec poids affiché
- **Sélection** : Nœuds verts lors de la sélection
- **Chemin optimal** : Nœuds dorés et arêtes rouges
- **Animation** : Transitions fluides lors des mises à jour

## 🔧 Extensibilité

### Ajouter un Nouvel Algorithme

1. Implémenter `PathFindingStrategy`
2. Ajouter à la liste dans `DrawingController.initialize()`
3. Ajouter le cas dans `setAlgorithm()`

### Ajouter de Nouvelles Commandes

1. Implémenter `Command`
2. Utiliser `CommandInvoker.executeCommand()`

## 📊 Métriques de Performance

- **Création de nœud** : O(1)
- **Création d'arête** : O(1)
- **Recherche de chemin** : Dépend de l'algorithme choisi
- **Mise à jour visuelle** : O(n) où n = nombre d'éléments dans le chemin

## 🎯 Avantages des Design Patterns

1. **Flexibilité** : Changement d'algorithme à l'exécution
2. **Extensibilité** : Ajout facile de nouveaux algorithmes
3. **Maintenabilité** : Code organisé et modulaire
4. **Réutilisabilité** : Composants indépendants
5. **Testabilité** : Chaque pattern peut être testé séparément

Cette implémentation démontre l'utilisation pratique des design patterns dans un contexte réel d'application graphique interactive.
