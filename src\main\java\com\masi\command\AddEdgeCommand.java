package com.masi.command;

import com.masi.graph.Graph;
import com.masi.graph.Edge;
import javafx.scene.layout.Pane;

/**
 * Commande pour ajouter une arête au graphe
 */
public class AddEdgeCommand implements Command {
    private Graph graph;
    private Pane drawingPane;
    private Edge edge;
    private boolean wasExecuted;

    public AddEdgeCommand(Graph graph, Pane drawingPane, Edge edge) {
        this.graph = graph;
        this.drawingPane = drawingPane;
        this.edge = edge;
        this.wasExecuted = false;
    }

    @Override
    public void execute() {
        if (!wasExecuted) {
            graph.addEdge(edge);
            drawingPane.getChildren().addAll(edge.getVisualEdge(), edge.getWeightLabel());
            wasExecuted = true;
        }
    }

    @Override
    public void undo() {
        if (wasExecuted) {
            // Note: Pour une implémentation complète, il faudrait une méthode removeEdge dans Graph
            drawingPane.getChildren().removeAll(edge.getVisualEdge(), edge.getWeightLabel());
            wasExecuted = false;
        }
    }

    @Override
    public String getDescription() {
        return "Add edge from " + edge.getSource().getId() + " to " + edge.getDestination().getId() + 
               " with weight " + edge.getWeight();
    }

    public Edge getEdge() {
        return edge;
    }
}
