package com.masi.algorithm;

import com.masi.graph.Graph;
import com.masi.graph.Node;
import java.util.List;

/**
 * Interface Strategy pour les algorithmes de recherche de plus court chemin
 */
public interface PathFindingStrategy {
    /**
     * Trouve le plus court chemin entre deux nœuds
     * @param graph Le graphe
     * @param start Le nœud de départ
     * @param end Le nœud d'arrivée
     * @return La liste des nœuds formant le plus court chemin, ou null si aucun chemin n'existe
     */
    List<Node> findShortestPath(Graph graph, Node start, Node end);
    
    /**
     * Retourne le nom de l'algorithme
     * @return Le nom de l'algorithme
     */
    String getAlgorithmName();
    
    /**
     * Retourne la distance totale du dernier chemin calculé
     * @return La distance totale
     */
    double getTotalDistance();
}
