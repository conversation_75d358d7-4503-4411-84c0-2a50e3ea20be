package com.masi.command;

import com.masi.graph.Graph;
import com.masi.graph.Node;
import javafx.scene.layout.Pane;

/**
 * Commande pour ajouter un nœud au graphe
 */
public class AddNodeCommand implements Command {
    private Graph graph;
    private Pane drawingPane;
    private Node node;
    private boolean wasExecuted;

    public AddNodeCommand(Graph graph, Pane drawingPane, Node node) {
        this.graph = graph;
        this.drawingPane = drawingPane;
        this.node = node;
        this.wasExecuted = false;
    }

    @Override
    public void execute() {
        if (!wasExecuted) {
            graph.addNode(node);
            drawingPane.getChildren().addAll(node.getVisualNode(), node.getLabel());
            wasExecuted = true;
        }
    }

    @Override
    public void undo() {
        if (wasExecuted) {
            // Note: Pour une implémentation complète, il faudrait une méthode removeNode dans Graph
            drawingPane.getChildren().removeAll(node.getVisualNode(), node.getLabel());
            wasExecuted = false;
        }
    }

    @Override
    public String getDescription() {
        return "Add node " + node.getId() + " at (" + node.getX() + ", " + node.getY() + ")";
    }

    public Node getNode() {
        return node;
    }
}
