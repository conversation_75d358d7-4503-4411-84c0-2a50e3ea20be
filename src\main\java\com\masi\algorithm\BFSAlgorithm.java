package com.masi.algorithm;

import com.masi.graph.Graph;
import com.masi.graph.Node;
import com.masi.graph.Edge;
import java.util.*;

/**
 * Implémentation de l'algorithme BFS (Breadth-First Search) pour trouver le plus court chemin
 * Note: BFS trouve le chemin avec le moins d'arêtes, pas nécessairement le plus court en distance
 */
public class BFSAlgorithm implements PathFindingStrategy {
    private double totalDistance;

    @Override
    public List<Node> findShortestPath(Graph graph, Node start, Node end) {
        if (start == null || end == null) {
            return null;
        }

        Queue<Node> queue = new LinkedList<>();
        Map<Node, Node> previous = new HashMap<>();
        Set<Node> visited = new HashSet<>();

        queue.offer(start);
        visited.add(start);
        previous.put(start, null);

        while (!queue.isEmpty()) {
            Node current = queue.poll();

            if (current.equals(end)) {
                // Chemin trouvé, le reconstruire
                List<Node> path = reconstructPath(previous, start, end);
                calculateTotalDistance(path, graph);
                return path;
            }

            // Examiner tous les voisins
            for (Edge edge : graph.getEdgesFromNode(current)) {
                Node neighbor = edge.getDestination();
                if (!visited.contains(neighbor)) {
                    visited.add(neighbor);
                    previous.put(neighbor, current);
                    queue.offer(neighbor);
                }
            }
        }

        // Aucun chemin trouvé
        return null;
    }

    private List<Node> reconstructPath(Map<Node, Node> previous, Node start, Node end) {
        List<Node> path = new ArrayList<>();
        Node current = end;

        while (current != null) {
            path.add(0, current);
            current = previous.get(current);
        }

        return path;
    }

    private void calculateTotalDistance(List<Node> path, Graph graph) {
        totalDistance = 0.0;
        
        for (int i = 0; i < path.size() - 1; i++) {
            Node current = path.get(i);
            Node next = path.get(i + 1);
            
            // Trouver l'arête entre current et next
            for (Edge edge : graph.getEdgesFromNode(current)) {
                if (edge.getDestination().equals(next)) {
                    totalDistance += edge.getWeight();
                    break;
                }
            }
        }
    }

    @Override
    public String getAlgorithmName() {
        return "BFS (Breadth-First Search)";
    }

    @Override
    public double getTotalDistance() {
        return totalDistance;
    }
}
