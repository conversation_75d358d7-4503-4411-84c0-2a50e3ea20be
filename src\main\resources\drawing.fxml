<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.geometry.Insets?>

<AnchorPane xmlns="http://javafx.com/javafx"
            xmlns:fx="http://javafx.com/fxml"
            fx:controller="com.masi.Controller.DrawingController"
            prefHeight="400.0" prefWidth="600.0">

    <children>
        <VBox alignment="CENTER" spacing="10">
            <padding>
                <Insets top="10" right="10" bottom="10" left="10"/>
            </padding>
            <HBox spacing="10" alignment="CENTER">
                <Label text="Shape:" />
                <ChoiceBox fx:id="shapeChoice" />
            </HBox>
            <HBox spacing="10" alignment="CENTER">
                <Label text="Logger:" />
                <ChoiceBox fx:id="logChoice" />
            </HBox>
            <HBox spacing="10" alignment="CENTER">
                <Label text="Decorator:" />
                <ChoiceBox fx:id="decoratorChoice" />
            </HBox>
            <Button text="Draw" onAction="#handleDraw"/>

            <Pane fx:id="drawingPane" prefHeight="300" prefWidth="500" style="-fx-background-color: white; -fx-border-color: black;" />
        </VBox>
    </children>
</AnchorPane>