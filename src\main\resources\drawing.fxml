<?xml version="1.0" encoding="UTF-8"?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.text.Font?>

<AnchorPane xmlns="http://javafx.com/javafx"
            xmlns:fx="http://javafx.com/fxml"
            fx:controller="com.masi.Controller.DrawingController"
            prefHeight="600.0" prefWidth="800.0"
            style="-fx-background-color: #f5f5f5;">

    <children>
        <BorderPane prefHeight="600.0" prefWidth="800.0">
            <top>
                <HBox alignment="CENTER" style="-fx-background-color: #4a90e2; -fx-padding: 15px;">
                    <Label text="Drawing Application - Design Patterns Demo" textFill="WHITE">
                        <font>
                            <Font name="System Bold" size="24.0"/>
                        </font>
                    </Label>
                </HBox>
            </top>

            <center>
                <VBox alignment="CENTER" spacing="20">
                    <padding>
                        <Insets top="20" right="20" bottom="20" left="20"/>
                    </padding>

                    <HBox spacing="20" alignment="CENTER">
                        <VBox spacing="10" style="-fx-background-color: white; -fx-padding: 15px; -fx-background-radius: 5px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 10, 0, 0, 10);">
                            <Label text="Drawing Options" style="-fx-font-weight: bold; -fx-font-size: 16px;"/>

                            <HBox spacing="10" alignment="CENTER_LEFT">
                                <Label text="Shape:" style="-fx-min-width: 80px;"/>
                                <ChoiceBox fx:id="shapeChoice" style="-fx-min-width: 150px;"/>
                            </HBox>

                            <HBox spacing="10" alignment="CENTER_LEFT">
                                <Label text="Logger:" style="-fx-min-width: 80px;"/>
                                <ChoiceBox fx:id="logChoice" style="-fx-min-width: 150px;"/>
                            </HBox>

                            <HBox spacing="10" alignment="CENTER_LEFT">
                                <Label text="Decorator:" style="-fx-min-width: 80px;"/>
                                <ChoiceBox fx:id="decoratorChoice" style="-fx-min-width: 150px;"/>
                            </HBox>

                            <Button text="Draw" onAction="#handleDraw"
                                    style="-fx-background-color: #4a90e2; -fx-text-fill: white; -fx-font-weight: bold; -fx-padding: 10 20; -fx-background-radius: 5;"/>
                        </VBox>
                    </HBox>

                    <Pane fx:id="drawingPane" prefHeight="400" prefWidth="700"
                          style="-fx-background-color: white; -fx-border-color: #dddddd; -fx-border-width: 1px; -fx-border-radius: 5px; -fx-background-radius: 5px; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 10, 0, 0, 10);" />
                </VBox>
            </center>

            <bottom>
                <HBox alignment="CENTER" style="-fx-background-color: #4a90e2; -fx-padding: 10px;">
                    <Label text="Patterns: Decorator | Observer | Strategy | MVC" textFill="WHITE"/>
                </HBox>
            </bottom>
        </BorderPane>
    </children>
</AnchorPane>